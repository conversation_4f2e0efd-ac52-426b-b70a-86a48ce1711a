package services

import (
	"context"
	"fmt"
	"time"

	"real-time-ca-service/internal/config"

	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
)

// RedisService handles Redis connections and distributed locking
type RedisService struct {
	client *redis.Client
	locker *redislock.Client
	config config.RedisConfig
}

// NewRedisService creates a new Redis service with the given configuration
func NewRedisService(cfg config.RedisConfig) (*RedisService, error) {
	// Set default values if not configured
	if cfg.Host == "" {
		cfg.Host = "localhost"
	}
	if cfg.Port == 0 {
		cfg.Port = 6379
	}
	if cfg.PoolSize == 0 {
		cfg.PoolSize = 10
	}
	if cfg.MinIdleConns == 0 {
		cfg.MinIdleConns = 2
	}
	if cfg.MaxRetries == 0 {
		cfg.MaxRetries = 3
	}
	if cfg.DialTimeout == 0 {
		cfg.DialTimeout = 5 * time.Second
	}
	if cfg.ReadTimeout == 0 {
		cfg.ReadTimeout = 3 * time.Second
	}
	if cfg.WriteTimeout == 0 {
		cfg.WriteTimeout = 3 * time.Second
	}
	if cfg.PoolTimeout == 0 {
		cfg.PoolTimeout = 4 * time.Second
	}
	if cfg.IdleTimeout == 0 {
		cfg.IdleTimeout = 5 * time.Minute
	}
	if cfg.IdleCheckFrequency == 0 {
		cfg.IdleCheckFrequency = 1 * time.Minute
	}

	// Set default distributed locking values
	if cfg.LockTimeout == 0 {
		cfg.LockTimeout = 30 * time.Second
	}
	if cfg.LockRetryDelay == 0 {
		cfg.LockRetryDelay = 100 * time.Millisecond
	}
	if cfg.LockMaxRetries == 0 {
		cfg.LockMaxRetries = 10
	}
	if cfg.LockRefreshInterval == 0 {
		cfg.LockRefreshInterval = 10 * time.Second
	}

	// Create Redis client
	client := redis.NewClient(&redis.Options{
		Addr:            fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:        cfg.Password,
		DB:              cfg.DB,
		PoolSize:        cfg.PoolSize,
		MinIdleConns:    cfg.MinIdleConns,
		MaxRetries:      cfg.MaxRetries,
		DialTimeout:     cfg.DialTimeout,
		ReadTimeout:     cfg.ReadTimeout,
		WriteTimeout:    cfg.WriteTimeout,
		PoolTimeout:     cfg.PoolTimeout,
		ConnMaxIdleTime: cfg.IdleTimeout,
		ConnMaxLifetime: cfg.IdleCheckFrequency,
	})

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	// Create distributed lock client
	locker := redislock.New(client)

	log.Info().
		Str("host", cfg.Host).
		Int("port", cfg.Port).
		Int("db", cfg.DB).
		Msg("Successfully connected to Redis")

	return &RedisService{
		client: client,
		locker: locker,
		config: cfg,
	}, nil
}

// Close closes the Redis connection
func (r *RedisService) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// GetClient returns the underlying Redis client
func (r *RedisService) GetClient() *redis.Client {
	return r.client
}

// GetLocker returns the distributed lock client
func (r *RedisService) GetLocker() *redislock.Client {
	return r.locker
}

// GetConfig returns the Redis configuration
func (r *RedisService) GetConfig() config.RedisConfig {
	return r.config
}

// Ping tests the Redis connection
func (r *RedisService) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}
