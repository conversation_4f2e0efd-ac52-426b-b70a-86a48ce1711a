package db

import (
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
)

// MigrateDatabase runs database migrations using GORM's AutoMigrate
func MigrateDatabase(db *Database) error {
	log.Info().Msg("Running database migrations with GORM...")

	// Run migrations
	start := time.Now()

	// Enable PostgreSQL extensions
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";").Error; err != nil {
		return fmt.Errorf("failed to create uuid-ossp extension: %w", err)
	}

	// Enable citext extension for case-insensitive text fields
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"citext\";").Error; err != nil {
		return fmt.Errorf("failed to create citext extension: %w", err)
	}

	// TODO: Manually, migrate before the tags table is generated
	// if err := db.Exec(`
	// 	INSERT INTO tweet_tags (tweet_id, tag_id)
	// 	SELECT tweets.id, 1
	// 	FROM tweets
	// 	WHERE NOT EXISTS (
	// 		SELECT 1
	// 		FROM tweet_tags
	// 		WHERE tweet_tags.tweet_id = tweets.id
	// 	)
	// `).Error; err != nil {
	// 	return fmt.Errorf("failed to add default tag to tweets: %w", err)
	// }

	// if err := db.Exec(`
	// 	INSERT INTO recognized_ca_tags (recognized_ca_id, tag_id)
	// 	SELECT recognized_cas.id, 1
	// 	FROM recognized_cas
	// 	WHERE NOT EXISTS (
	// 		SELECT 1
	// 		FROM recognized_ca_tags
	// 		WHERE recognized_ca_tags.recognized_ca_id = recognized_cas.id
	// 	)
	// `).Error; err != nil {
	// 	return fmt.Errorf("failed to add default tag to recognized_cas: %w", err)
	// }

	// Auto migrate all models
	if err := db.AutoMigrate(
		&TwitterUser{},
		&Tweet{},
		&RecognizedCA{},
		&TokenDetails{},
		&Tag{},
		&RecognizedCATag{},
		&TweetTag{},
	); err != nil {
		return fmt.Errorf("failed to run auto migrations: %w", err)
	}

	log.Info().
		Dur("duration", time.Since(start)).
		Msg("Database migrations completed successfully")

	return nil
}
