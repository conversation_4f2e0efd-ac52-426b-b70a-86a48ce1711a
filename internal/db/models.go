package db

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// don't use soft delete
type BaseModel struct {
	ID        int64     `gorm:"type:bigserial;primaryKey;autoIncrement"`
	CreatedAt time.Time `gorm:"type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `gorm:"type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP"`
}

// JSONB is a type for handling PostgreSQL JSONB data
type JSONB map[string]interface{}

// Value implements the driver.Valuer interface for JSONB
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// <PERSON>an implements the sql.Scanner interface for JSONB
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, j)
}

// TwitterUser represents a Twitter user
type TwitterUser struct {
	BaseModel
	UserID          string    `gorm:"column:user_id;uniqueIndex;type:varchar(255)"`
	ScreenName      string    `gorm:"column:screen_name;uniqueIndex;type:varchar(100);not null"`
	Name            string    `gorm:"column:name;type:varchar(255);not null"`
	FollowersCount  int       `gorm:"column:followers_count;type:integer"`
	IsVerified      bool      `gorm:"column:is_verified;type:boolean;default:false"`
	ProfileImageURL string    `gorm:"column:profile_image_url;type:text"`
	FetchedAt       time.Time `gorm:"column:fetched_at;type:timestamp with time zone;default:current_timestamp"`

	// Relationships
	Tweets []Tweet `gorm:"foreignKey:UserIDFK;references:UserID"`
}

// TableName specifies the table name for TwitterUser
func (TwitterUser) TableName() string {
	return "twitter_users"
}

// Tag represents a tag used for classification
type Tag struct {
	BaseModel
	Name        string    `gorm:"column:name;uniqueIndex;type:citext"`
	Description string    `gorm:"column:description;type:text"`
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP"`
}

// TableName specifies the table name for Tag
func (Tag) TableName() string {
	return "tags"
}

// TweetTag represents the many-to-many relationship between tweets and tags
type TweetTag struct {
	TweetID int64 `gorm:"primaryKey;column:tweet_id"`
	TagID   int64 `gorm:"primaryKey;column:tag_id"`
}

// TableName specifies the table name for TweetTag
func (TweetTag) TableName() string {
	return "tweet_tags"
}

// RecognizedCATag represents the many-to-many relationship between recognized CAs and tags
type RecognizedCATag struct {
	RecognizedCAID int64 `gorm:"primaryKey;column:recognized_ca_id"`
	TagID          int64 `gorm:"primaryKey;column:tag_id"`
}

// TableName specifies the table name for RecognizedCATag
func (RecognizedCATag) TableName() string {
	return "recognized_ca_tags"
}

// Tweet represents a tweet
type Tweet struct {
	BaseModel
	TweetID                string     `gorm:"column:tweet_id;uniqueIndex:idx_tweet_published,priority:1;type:varchar(255)"`
	UserIDFK               string     `gorm:"column:user_id_fk;type:varchar(255);not null;index"`
	TextContent            string     `gorm:"column:text_content;type:text;not null"`
	FullTweetJSON          JSONB      `gorm:"column:full_tweet_json;type:jsonb;index:idx_tweets_full_tweet_json,type:gin"`
	PublishedAt            time.Time  `gorm:"column:published_at;uniqueIndex:idx_tweet_published,priority:2;type:timestamp with time zone;not null"`
	ViewsCount             int        `gorm:"column:views_count;type:bigint"`
	ReplyCount             int        `gorm:"column:reply_count;type:integer;default:0"`
	RetweetCount           int        `gorm:"column:retweet_count;type:integer;default:0"`
	FavoriteCount          int        `gorm:"column:favorite_count;type:integer;default:0"`
	BookmarkCount          int        `gorm:"column:bookmark_count;type:integer;default:0"`
	ContainsTargetKeyword  bool       `gorm:"column:contains_target_keyword;type:boolean;default:false;index"`
	IngestedAt             time.Time  `gorm:"column:ingested_at;type:timestamp with time zone;default:current_timestamp"`
	TweetType              string     `gorm:"column:tweet_type;type:varchar(50);default:'tweet'"`         // tweet or article
	ContentType            string     `gorm:"column:content_type;type:varchar(50);default:'tweet'"`       // tweet or reply or quote
	SourceListType         string     `gorm:"column:source_list_type;type:varchar(50);default:'unknown'"` // KOLs or Projects
	AIJudgment             string     `gorm:"column:ai_judgment;type:varchar(10);default:'NO'"`           // YES or NO
	ArticleTitle           string     `gorm:"column:article_title;type:text"`                             // Article title for article type tweets
	ArticlePreviewText     string     `gorm:"column:article_preview_text;type:text"`                      // Article preview text for article type tweets
	ArticleCoverURL        string     `gorm:"column:article_cover_url;type:text"`                         // Article cover URL for article type tweets
	FullArticleJSON        JSONB      `gorm:"column:full_article_json;type:jsonb;index:idx_tweets_full_article_json,type:gin"`
	BulletPoints           JSONB      `gorm:"column:bullet_points;type:jsonb"`                                    // Bullet points for article type tweets
	PeriodicallyUpdatedAt  *time.Time `gorm:"column:periodically_updated_at;type:timestamp with time zone;index"` // Tracks when the tweet was last updated by the periodic updater
	IsProductUpdate        bool       `gorm:"column:is_product_update;type:boolean;default:false"`
	IsBusinessData         bool       `gorm:"column:is_business_data;type:boolean;default:false"`
	IsEcosystemPartnership bool       `gorm:"column:is_ecosystem_partnership;type:boolean;default:false"`
	IsProfitOpportunity    bool       `gorm:"column:is_profit_opportunity;type:boolean;default:false"`
	IsIndustryEvent        bool       `gorm:"column:is_industry_event;type:boolean;default:false"`
	IsOthers               bool       `gorm:"column:is_others;type:boolean;default:false"`

	// Relationships
	User         *TwitterUser   `gorm:"foreignKey:UserIDFK;references:UserID"`
	ExtractedCAs []RecognizedCA `gorm:"many2many:tweet_contract_addresses;"`
	Tags         []Tag          `gorm:"many2many:tweet_tags;"`
}

// TableName specifies the table name for Tweet
func (Tweet) TableName() string {
	return "tweets"
}

type RecognizedCA struct {
	BaseModel
	CAAddress            string     `gorm:"column:ca_address;uniqueIndex;type:varchar(255)"`
	ChainType            string     `gorm:"column:chain_type;type:varchar(100);not null;index"`
	TokenNameHint        string     `gorm:"column:token_name_hint;type:varchar(255)"`
	AddedAt              time.Time  `gorm:"column:added_at;type:timestamp with time zone;default:current_timestamp"`
	LastTweetAt          time.Time  `gorm:"column:last_tweet_at;type:timestamp with time zone;default:current_timestamp"`
	LastCheckedForDataAt *time.Time `gorm:"column:last_checked_for_data_at;type:timestamp with time zone;index"`
	ReferenceCount       int        `gorm:"column:reference_count;type:int;default:0"`

	// Relationships
	Tweets       []Tweet        `gorm:"many2many:tweet_contract_addresses;"`
	TokenDetails []TokenDetails `gorm:"foreignKey:CAAddressFK;references:CAAddress"` // Changed to one-to-many
	Tags         []Tag          `gorm:"many2many:recognized_ca_tags;"`
}

// TableName specifies the table name for RecognizedCA
func (RecognizedCA) TableName() string {
	return "recognized_cas"
}

// TokenDetails represents token details fetched from DexScreener or other sources
type TokenDetails struct {
	BaseModel
	CAAddressFK         string     `gorm:"column:ca_address_fk;type:varchar(255);index;uniqueIndex:idx_token_details_ca_chain,priority:1"` // Added composite uniqueIndex
	ChainID             string     `gorm:"column:chain_id;type:varchar(100);not null;index;uniqueIndex:idx_token_details_ca_chain,priority:2"`
	Source              string     `gorm:"column:source;type:varchar(50);not null;default:'dexscreener'"`
	TokenName           string     `gorm:"column:token_name;type:varchar(255)"`
	Symbol              string     `gorm:"column:symbol;type:varchar(50)"`
	TokenLogoURL        string     `gorm:"column:token_logo_url;type:text"`
	TokenTwitterURL     string     `gorm:"column:token_twitter_url;type:text"`
	PairCreatedAt       *time.Time `gorm:"column:pair_created_at;type:timestamp with time zone"`
	TokenCreatedAt      *time.Time `gorm:"column:token_created_at;type:timestamp with time zone"`
	HolderCount         *int64     `gorm:"column:holder_count;type:bigint"`
	MarketCapUSD        *float64   `gorm:"column:market_cap_usd;type:numeric(30,8)"`
	PriceUSD            *float64   `gorm:"column:price_usd;type:numeric(30,18)"`
	FullDexScreenerJSON string     `gorm:"column:full_dexscreener_json;type:jsonb;default:'{}';index:idx_token_details_full_dexscreener_json,type:gin"`
	LastUpdatedAt       time.Time  `gorm:"column:last_updated_at;type:timestamp with time zone;default:current_timestamp"`

	// Relationships
	RecognizedCA *RecognizedCA `gorm:"foreignKey:CAAddressFK;references:CAAddress"`
}

// TableName specifies the table name for TokenDetails
func (TokenDetails) TableName() string {
	return "token_details"
}

// Database represents a database connection
type Database struct {
	DB *gorm.DB
}

// Helper methods to make Database struct methods work with the embedded DB

func (d *Database) Clauses(conds ...clause.Expression) *gorm.DB {
	return d.DB.Clauses(conds...)
}

func (d *Database) Where(query interface{}, args ...interface{}) *gorm.DB {
	return d.DB.Where(query, args...)
}

func (d *Database) Preload(query string, args ...interface{}) *gorm.DB {
	return d.DB.Preload(query, args...)
}

func (d *Database) Raw(sql string, values ...interface{}) *gorm.DB {
	return d.DB.Raw(sql, values...)
}

func (d *Database) Order(value interface{}) *gorm.DB {
	return d.DB.Order(value)
}

func (d *Database) Limit(limit int) *gorm.DB {
	return d.DB.Limit(limit)
}

func (d *Database) Offset(offset int) *gorm.DB {
	return d.DB.Offset(offset)
}

func (d *Database) Find(dest interface{}, conds ...interface{}) *gorm.DB {
	return d.DB.Find(dest, conds...)
}

func (d *Database) First(dest interface{}, conds ...interface{}) *gorm.DB {
	return d.DB.First(dest, conds...)
}

func (d *Database) Create(value interface{}) *gorm.DB {
	return d.DB.Create(value)
}

func (d *Database) Delete(value interface{}, conds ...interface{}) *gorm.DB {
	return d.DB.Delete(value, conds...)
}

func (d *Database) Model(value interface{}) *gorm.DB {
	return d.DB.Model(value)
}

func (d *Database) Transaction(fc func(tx *gorm.DB) error, opts ...*sql.TxOptions) error {
	return d.DB.Transaction(fc, opts...)
}

func (d *Database) Exec(sql string, values ...interface{}) *gorm.DB {
	return d.DB.Exec(sql, values...)
}

func (d *Database) AutoMigrate(dst ...interface{}) error {
	return d.DB.AutoMigrate(dst...)
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
