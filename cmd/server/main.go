package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/ai"
	"real-time-ca-service/internal/api"
	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/db"
	"real-time-ca-service/internal/logger"
	"real-time-ca-service/internal/services"

	_ "real-time-ca-service/docs"
)

// @title           Real-Time CA Service API
// @version         1.0
// @description     API for the Real-Time Contract Address service
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8080
// @BasePath  /api

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
func main() {
	// Parse command-line flags
	flags := config.ParseCommandLineFlags()

	// Load configuration with command-line flag overrides
	cfg, err := config.LoadWithFlags(flags)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Initialize logger
	logger.Init(cfg.Logging.Level, cfg.Logging.Pretty)
	log.Info().Msg("Starting real-time CA service")

	// Initialize database
	database, err := db.Connect(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}
	defer database.Close()

	// Run database migrations with GORM if enabled in config
	if cfg.Database.MigrateOnStartup {
		if err := db.MigrateDatabase(database); err != nil {
			log.Fatal().Err(err).Msg("Failed to run database migrations")
		}
		log.Info().Msg("Database migrations completed")
	} else {
		log.Info().Msg("Database migrations skipped due to configuration")
	}

	// Initialize AI service
	aiService := ai.NewService(ai.Config{
		Enabled:        cfg.AI.Enabled,
		APIKey:         cfg.AI.APIKey,
		Model:          cfg.AI.Model,
		BaseURL:        cfg.AI.BaseURL,
		MaxRetry:       cfg.AI.MaxRetry,
		RequestsPerSec: cfg.AI.RequestsPerSec,
	})

	// Initialize Redis service
	redisService, err := services.NewRedisService(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis service")
	}
	defer redisService.Close()

	// Initialize Telegram service
	telegramService := services.NewTelegramService(&cfg.Telegram)

	// Initialize services
	twitterService := services.NewTwitterService(cfg.SocialData, database, aiService, telegramService, redisService)
	tokenService := services.NewTokenService(cfg.DexScreener, cfg.Moralis, database)
	caService := services.NewCAService(database, aiService, tokenService)

	// Set up service dependencies
	twitterService.SetCAService(caService)
	tokenService.SetCAService(caService)

	// Start background workers only if OnlyAPI is false
	if !cfg.Server.OnlyAPI {
		log.Info().Msg("Starting background workers...")
		twitterService.StartMonitoring()
		tokenService.StartTokenUpdater()
	} else {
		log.Info().Msg("Only API mode enabled, background workers not started")
	}

	// Initialize API router

	// Create HTTP server
	server := &http.Server{}

	// Start server in a goroutine only if enabled in config
	if cfg.Server.Enable {
		router := api.NewRouter(twitterService, caService, tokenService, cfg)
		server = &http.Server{
			Addr:         cfg.Server.Address,
			Handler:      router,
			ReadTimeout:  cfg.Server.ReadTimeout,
			WriteTimeout: cfg.Server.WriteTimeout,
		}
		go func() {
			log.Info().Str("address", cfg.Server.Address).Msg("Server starting")
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.Fatal().Err(err).Msg("Failed to start server")
			}
		}()
	} else {
		log.Info().Msg("HTTP server not started due to configuration")
	}

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info().Msg("Shutting down server...")

	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Server.ShutdownTimeout)
	defer cancel()

	// Stop background workers only if they were started (OnlyAPI is false)
	if !cfg.Server.OnlyAPI {
		log.Info().Msg("Stopping background workers...")
		twitterService.StopMonitoring()
		tokenService.StopTokenUpdater()
	} else {
		log.Info().Msg("Only API mode enabled, no background workers to stop")
	}

	// Shutdown server
	log.Info().Msg("Shutting down HTTP server...")
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal().Err(err).Msg("Server forced to shutdown")
	}

	log.Info().Msg("Server exited properly")
}
