# Distributed Locking Implementation

## Overview

This document describes the Redis-based distributed locking implementation for the real-time CA service. The distributed locking mechanism prevents race conditions during concurrent tweet processing across multiple service instances.

## Architecture

### Components

1. **RedisService**: Manages Redis connections and provides the foundation for distributed locking
2. **DistributedLockManager**: High-level interface for acquiring and managing distributed locks
3. **DistributedLock**: Represents an individual acquired lock with automatic refresh capabilities
4. **TwitterService Integration**: Uses distributed locking in `ProcessListTweet` method

### Key Features

- **Atomic Lock Operations**: Uses Redis SET with NX (not exists) and EX (expiration) options via Lua scripts
- **Automatic Lock Refresh**: Prevents lock expiration during long-running operations
- **Configurable Retry Strategy**: Exponential backoff with configurable parameters
- **Production-Ready Error Handling**: Comprehensive error handling and logging
- **Graceful Degradation**: Service continues to function even if Redis is unavailable

## Configuration

### Redis Configuration (service.toml)

```toml
[redis]
# Redis connection details
host = "localhost"
port = 6379
password = ""
db = 0

# Connection pool settings
pool_size = 10
min_idle_conns = 2
max_retries = 3

# Timeout settings (in seconds)
dial_timeout_sec = 5
read_timeout_sec = 3
write_timeout_sec = 3
pool_timeout_sec = 4
idle_timeout_sec = 300
idle_check_frequency_sec = 60

# Distributed locking configuration
lock_timeout_sec = 30          # Default lock TTL
lock_retry_delay_sec = 1       # Delay between lock acquisition retries
lock_max_retries = 10          # Maximum number of lock acquisition retries
lock_refresh_interval_sec = 10 # Interval for automatic lock refresh
```

### Environment-Specific Recommendations

#### Development
- `lock_timeout_sec = 10`
- `lock_max_retries = 5`
- `lock_retry_delay_sec = 1`

#### Production
- `lock_timeout_sec = 30`
- `lock_max_retries = 10`
- `lock_retry_delay_sec = 1`
- `lock_refresh_interval_sec = 10`

## Usage

### Basic Lock Acquisition

```go
// Create lock manager
lockManager := NewDistributedLockManager(redisService)

// Acquire lock with default options
lock, err := lockManager.AcquireLock(ctx, "my_lock_key", nil)
if err != nil {
    log.Error().Err(err).Msg("Failed to acquire lock")
    return
}

// Ensure lock is released
defer func() {
    if releaseErr := lock.ReleaseLock(); releaseErr != nil {
        log.Error().Err(releaseErr).Msg("Error releasing lock")
    }
}()

// Perform critical section work
// ...
```

### Custom Lock Options

```go
opts := &LockOptions{
    Timeout:    30 * time.Second,
    RetryDelay: 100 * time.Millisecond,
    MaxRetries: 5,
    Metadata:   "tweet_processing",
}

lock, err := lockManager.AcquireLock(ctx, lockKey, opts)
```

### Tweet Processing Integration

The `ProcessListTweet` method automatically uses distributed locking:

```go
func (s *TwitterService) ProcessListTweet(tweet Tweet, listType string) {
    // Distributed lock is automatically acquired using tweet.IdStr
    lockKey := CreateTweetLockKey(tweet.IdStr)
    
    // Lock is held during the entire tweet processing operation
    // Lock is automatically released when the function exits
}
```

## Lock Key Strategy

### Tweet Processing Locks
- **Pattern**: `tweet_lock:{tweet_id}`
- **Example**: `tweet_lock:1234567890123456789`
- **Purpose**: Prevents concurrent processing of the same tweet

### Lock Key Best Practices
1. Use descriptive prefixes (e.g., `tweet_lock:`, `user_lock:`)
2. Include unique identifiers (tweet ID, user ID, etc.)
3. Keep keys reasonably short but descriptive
4. Use consistent naming conventions

## Error Handling

### Lock Acquisition Failures
- **ErrNotObtained**: Lock is already held by another instance
- **Network Errors**: Redis connection issues
- **Timeout Errors**: Lock acquisition timeout exceeded

### Graceful Degradation
If Redis is unavailable:
1. Log warning about distributed locking being disabled
2. Continue processing without locking (single-instance mode)
3. Monitor Redis connectivity for automatic recovery

### Error Recovery
- Automatic retry with exponential backoff
- Lock expiration prevents deadlocks
- Connection pool handles Redis reconnection

## Monitoring and Metrics

### Key Metrics to Monitor
1. **Lock Acquisition Rate**: Successful vs failed lock acquisitions
2. **Lock Hold Time**: How long locks are held on average
3. **Lock Contention**: Frequency of lock acquisition failures
4. **Redis Connection Health**: Connection pool status

### Logging
- Lock acquisition attempts (DEBUG level)
- Successful lock acquisitions (INFO level)
- Lock acquisition failures (WARN level)
- Lock release operations (INFO level)
- Lock refresh operations (DEBUG level)

## Operational Considerations

### Deployment
1. Ensure Redis is deployed with high availability (Redis Sentinel or Cluster)
2. Configure appropriate Redis memory limits and eviction policies
3. Monitor Redis performance and connection limits
4. Set up Redis backup and recovery procedures

### Scaling
- Lock contention increases with the number of service instances
- Consider sharding strategies for high-throughput scenarios
- Monitor lock acquisition latency and adjust retry parameters

### Troubleshooting

#### High Lock Contention
- Increase `lock_retry_delay_sec` to reduce Redis load
- Decrease `lock_timeout_sec` if operations are quick
- Consider implementing lock-free algorithms for high-frequency operations

#### Lock Acquisition Timeouts
- Check Redis connectivity and performance
- Verify lock timeout settings are appropriate for operation duration
- Monitor for deadlock scenarios (should be prevented by TTL)

#### Memory Usage
- Monitor Redis memory usage for lock keys
- Ensure locks are properly released
- Set appropriate TTL values to prevent memory leaks

## Security Considerations

1. **Redis Authentication**: Use strong passwords for Redis instances
2. **Network Security**: Secure Redis network access with firewalls/VPNs
3. **Lock Metadata**: Avoid storing sensitive information in lock metadata
4. **Access Control**: Implement proper Redis ACLs in production

## Testing

### Unit Tests
- Lock acquisition and release
- Concurrent lock attempts
- Lock refresh mechanism
- Error handling scenarios

### Integration Tests
- Redis connectivity
- Multi-instance lock contention
- Network partition scenarios
- Redis failover testing

### Load Testing
- High-frequency lock operations
- Multiple concurrent instances
- Redis performance under load
- Lock acquisition latency measurement
