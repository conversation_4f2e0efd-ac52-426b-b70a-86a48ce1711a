// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/announcement-statistics": {
            "get": {
                "description": "Get statistics for tweets with source_list_type=\"Projects\" and ai_judgment=\"YES\" grouped by Twitter user. Optional time filtering with start_time and end_time query parameters (Unix timestamps). Optional sorting with sort_field and sort_direction parameters.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "statistics"
                ],
                "summary": "Get announcement statistics",
                "parameters": [
                    {
                        "type": "integer",
                        "example": **********,
                        "description": "Start time (Unix timestamp, inclusive)",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 1672531199,
                        "description": "End time (Unix timestamp, inclusive)",
                        "name": "end_time",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "product_updates",
                            "business_data",
                            "ecosystem_partnership",
                            "profit_opportunity",
                            "industry_events",
                            "others",
                            "total"
                        ],
                        "type": "string",
                        "description": "Field to sort by",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Sort direction",
                        "name": "sort_direction",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.UserAnnouncementStatsResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "Returns 200 OK if the service is healthy",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "system"
                ],
                "summary": "Health check endpoint",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/list-tweets": {
            "get": {
                "description": "Retrieves a list of tweets related to AI Agent with filtering options",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tweets"
                ],
                "summary": "Get AI Agent related tweets",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of tweets to return (default: 20)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by content type (tweet, article, ALL)",
                        "name": "content_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by source type (KOLs, Projects, ALL)",
                        "name": "source_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by notice_type",
                        "name": "notice_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by user_id",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by user_name",
                        "name": "user_name",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "tags",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.TweetResponse"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/recognized-ca/{address}/{chain_id}": {
            "get": {
                "description": "Retrieves a specific recognized contract address by its address and chain ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "contract-addresses"
                ],
                "summary": "Get a single recognized contract address",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Contract address",
                        "name": "address",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Chain ID",
                        "name": "chain_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.CAResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/recognized-cas": {
            "get": {
                "description": "Retrieves a list of recognized contract addresses",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "contract-addresses"
                ],
                "summary": "Get recognized contract addresses",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of CAs to return (default: 20)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "tags",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.CAResponse"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/tweet/{tweet_id}": {
            "get": {
                "description": "Retrieves a single tweet by its ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tweets"
                ],
                "summary": "Get a tweet by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Tweet ID",
                        "name": "tweet_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.TweetResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/tweets": {
            "get": {
                "description": "Retrieves a list of tweets",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tweets"
                ],
                "summary": "Get tweets",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of tweets to return (default: 20)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "tags",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.TweetResponse"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/webhook/twitter": {
            "post": {
                "description": "Processes webhook events from Twitter",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "webhooks"
                ],
                "summary": "Handle Twitter webhook",
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        }
    },
    "definitions": {
        "api.CAResponse": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "chain_type": {
                    "type": "string"
                },
                "is_recognized": {
                    "type": "boolean"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "token_details": {
                    "description": "Changed to array",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.TokenDetailsResponse"
                    }
                },
                "trade_token_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.TradeTokenInfo"
                    }
                },
                "tweets": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.TweetInfo"
                    }
                }
            }
        },
        "api.NoticeResponse": {
            "type": "object",
            "properties": {
                "is_business_data": {
                    "type": "boolean"
                },
                "is_ecosystem_partnership": {
                    "type": "boolean"
                },
                "is_industry_event": {
                    "type": "boolean"
                },
                "is_other": {
                    "type": "boolean"
                },
                "is_product_update": {
                    "type": "boolean"
                },
                "is_profit_opportunity": {
                    "type": "boolean"
                }
            }
        },
        "api.TokenDetailsResponse": {
            "type": "object",
            "properties": {
                "chain_id": {
                    "type": "string"
                },
                "holder_count": {
                    "type": "integer"
                },
                "logo_url": {
                    "type": "string"
                },
                "market_cap_usd": {
                    "type": "number"
                },
                "name": {
                    "type": "string"
                },
                "pair_created_at": {
                    "type": "integer"
                },
                "price_usd": {
                    "type": "number"
                },
                "source": {
                    "description": "Added source field",
                    "type": "string"
                },
                "symbol": {
                    "type": "string"
                },
                "twitter_url": {
                    "type": "string"
                }
            }
        },
        "api.TradeTokenInfo": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "ai_report": {
                    "type": "string"
                },
                "banner_url": {
                    "type": "string"
                },
                "chain_id": {
                    "type": "integer"
                },
                "coingecko_url": {
                    "type": "string"
                },
                "coinmarketcap_url": {
                    "type": "string"
                },
                "creation_date": {
                    "type": "string"
                },
                "creator_x_username": {
                    "type": "string"
                },
                "decimals": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "discord_url": {
                    "type": "string"
                },
                "followers_count": {
                    "type": "integer"
                },
                "github_url": {
                    "type": "string"
                },
                "influencers_count": {
                    "type": "integer"
                },
                "instagram_username": {
                    "type": "string"
                },
                "is_verified": {
                    "type": "boolean"
                },
                "is_watched": {
                    "type": "boolean"
                },
                "logo_url": {
                    "type": "string"
                },
                "market_cap": {
                    "type": "string"
                },
                "medium_url": {
                    "type": "string"
                },
                "mobile_banner_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "price_change_in_1hours": {
                    "type": "string"
                },
                "price_change_in_24hours": {
                    "type": "string"
                },
                "price_change_in_6hours": {
                    "type": "string"
                },
                "price_in_usd": {
                    "type": "string"
                },
                "profile": {
                    "type": "string"
                },
                "project_url": {
                    "type": "string"
                },
                "projects_count": {
                    "type": "integer"
                },
                "rank": {
                    "type": "integer"
                },
                "reddit_url": {
                    "type": "string"
                },
                "research_report": {
                    "type": "string"
                },
                "slug": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "symbol": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {}
                },
                "telegram_url": {
                    "type": "string"
                },
                "tiktok_url": {
                    "type": "string"
                },
                "top_20_followers": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "avatar": {
                                "type": "string"
                            },
                            "name": {
                                "type": "string"
                            },
                            "username": {
                                "type": "string"
                            }
                        }
                    }
                },
                "total_buy_count_24hours": {
                    "type": "string"
                },
                "total_buyer_count_24hours": {
                    "type": "string"
                },
                "total_liquidity": {
                    "type": "string"
                },
                "total_makers_count_24hours": {
                    "type": "string"
                },
                "total_sell_count_24hours": {
                    "type": "string"
                },
                "total_seller_count_24hours": {
                    "type": "string"
                },
                "total_supply": {
                    "type": "string"
                },
                "total_tx_count_24hours": {
                    "type": "string"
                },
                "total_volume_in_1hours": {
                    "type": "string"
                },
                "total_volume_in_24hours": {
                    "type": "string"
                },
                "total_volume_in_6hours": {
                    "type": "string"
                },
                "twitter_score": {
                    "type": "string"
                },
                "twitter_user_id": {
                    "type": "string"
                },
                "twitter_username": {
                    "type": "string"
                },
                "type": {
                    "type": "integer"
                },
                "venture_capitals_count": {
                    "type": "integer"
                },
                "warpcast_url": {
                    "type": "string"
                }
            }
        },
        "api.TweetInfo": {
            "type": "object",
            "properties": {
                "article_cover_url": {
                    "type": "string"
                },
                "article_preview_text": {
                    "type": "string"
                },
                "article_title": {
                    "type": "string"
                },
                "bookmark_count": {
                    "type": "integer"
                },
                "bullet_points": {},
                "content_type": {
                    "description": "\"tweet\" or \"article\"",
                    "type": "string"
                },
                "favorite_count": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "images": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "notices": {
                    "$ref": "#/definitions/api.NoticeResponse"
                },
                "published_at": {
                    "type": "integer"
                },
                "reply_count": {
                    "type": "integer"
                },
                "retweet_count": {
                    "type": "integer"
                },
                "source_list_type": {
                    "description": "\"KOLs\" or \"Projects\"",
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "text": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api.UserResponse"
                },
                "views_count": {
                    "type": "integer"
                }
            }
        },
        "api.TweetResponse": {
            "type": "object",
            "properties": {
                "article_cover_url": {
                    "type": "string"
                },
                "article_preview_text": {
                    "type": "string"
                },
                "article_title": {
                    "type": "string"
                },
                "bookmark_count": {
                    "type": "integer"
                },
                "bullet_points": {},
                "content_type": {
                    "description": "\"tweet\" or \"article\"",
                    "type": "string"
                },
                "contract_addresses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.CAResponse"
                    }
                },
                "favorite_count": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "images": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "notices": {
                    "$ref": "#/definitions/api.NoticeResponse"
                },
                "published_at": {
                    "type": "integer"
                },
                "reply_count": {
                    "type": "integer"
                },
                "retweet_count": {
                    "type": "integer"
                },
                "source_list_type": {
                    "description": "\"KOLs\" or \"Projects\"",
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "text": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api.UserResponse"
                },
                "views_count": {
                    "type": "integer"
                }
            }
        },
        "api.TwitterUserInfo": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "screen_name": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "api.UserAnnouncementStatsResponse": {
            "type": "object",
            "properties": {
                "business_data_count": {
                    "type": "integer"
                },
                "ecosystem_partnership_count": {
                    "type": "integer"
                },
                "industry_events_count": {
                    "type": "integer"
                },
                "others_count": {
                    "type": "integer"
                },
                "product_updates_count": {
                    "type": "integer"
                },
                "profit_opportunity_count": {
                    "type": "integer"
                },
                "total_announcements_count": {
                    "type": "integer"
                },
                "twitter_user": {
                    "$ref": "#/definitions/api.TwitterUserInfo"
                }
            }
        },
        "api.UserResponse": {
            "type": "object",
            "properties": {
                "followers_count": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "is_verified": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "profile_image_url": {
                    "type": "string"
                },
                "screen_name": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api",
	Schemes:          []string{},
	Title:            "Real-Time CA Service API",
	Description:      "API for the Real-Time Contract Address service",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
